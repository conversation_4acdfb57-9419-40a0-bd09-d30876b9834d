#!/bin/bash

# 修复404和重定向问题

set -e

echo "=== 修复404和重定向问题 ==="

# 1. 检查当前nginx配置
echo "步骤1: 检查nginx配置..."
if sudo nginx -t; then
    echo "✅ nginx配置语法正确"
else
    echo "❌ nginx配置有语法错误"
    exit 1
fi

# 2. 检查前端文件目录
echo ""
echo "步骤2: 检查前端文件..."

# 检查可能的前端目录
POSSIBLE_DIRS=(
    "/usr/local/nginx/html/dist_keycloak"
    "/usr/local/nginx/html/dist"
    "web/dist"
    "web/dist_keycloak"
)

FRONTEND_DIR=""
for dir in "${POSSIBLE_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "找到前端目录: $dir"
        if [ -f "$dir/index.html" ]; then
            echo "✅ 找到index.html: $dir/index.html"
            FRONTEND_DIR="$dir"
            break
        else
            echo "⚠️  目录存在但缺少index.html: $dir"
        fi
    fi
done

if [ -z "$FRONTEND_DIR" ]; then
    echo "❌ 未找到有效的前端目录"
    echo "正在检查web目录..."
    
    if [ -d "web" ]; then
        echo "找到web目录，尝试构建前端..."
        cd web
        
        if [ -f "package.json" ]; then
            echo "安装依赖..."
            npm install
            
            echo "构建前端..."
            npm run build
            
            if [ -d "dist" ]; then
                echo "✅ 前端构建成功"
                FRONTEND_DIR="$(pwd)/dist"
                cd ..
            else
                echo "❌ 前端构建失败"
                cd ..
                exit 1
            fi
        else
            echo "❌ 未找到package.json"
            cd ..
            exit 1
        fi
    else
        echo "❌ 未找到web目录"
        echo "创建临时前端文件..."
        
        # 创建临时前端目录和文件
        sudo mkdir -p /usr/local/nginx/html/dist_keycloak
        sudo tee /usr/local/nginx/html/dist_keycloak/index.html > /dev/null << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XinHe Workbench - 临时页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        .title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }
        .status {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: left;
        }
        .info-item {
            margin: 8px 0;
            font-family: monospace;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀</div>
        <h1 class="title">XinHe Workbench</h1>
        <p class="subtitle">芯合跨架构智算软件工厂</p>
        
        <div class="status">
            ✅ 服务器运行正常
        </div>
        
        <div class="info">
            <div class="info-item"><strong>时间:</strong> <span id="time"></span></div>
            <div class="info-item"><strong>协议:</strong> <span id="protocol"></span></div>
            <div class="info-item"><strong>端口:</strong> <span id="port"></span></div>
            <div class="info-item"><strong>状态:</strong> 等待前端应用部署</div>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="/api/health" class="btn">检查API</a>
            <button onclick="location.reload()" class="btn">刷新页面</button>
        </div>
    </div>
    
    <script>
        function updateInfo() {
            document.getElementById('time').textContent = new Date().toLocaleString('zh-CN');
            document.getElementById('protocol').textContent = location.protocol;
            document.getElementById('port').textContent = location.port || (location.protocol === 'https:' ? '443' : '80');
        }
        
        updateInfo();
        setInterval(updateInfo, 1000);
        
        // 每10秒检查一次真正的应用是否可用
        setInterval(() => {
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    if (data.service !== 'nginx') {
                        // 如果API返回的不是nginx的健康检查，说明真正的应用已部署
                        window.location.reload();
                    }
                })
                .catch(() => {
                    // API还未就绪
                });
        }, 10000);
    </script>
</body>
</html>
EOF
        FRONTEND_DIR="/usr/local/nginx/html/dist_keycloak"
        echo "✅ 创建了临时前端页面"
    fi
fi

# 3. 复制前端文件到正确位置
echo ""
echo "步骤3: 部署前端文件..."
if [ "$FRONTEND_DIR" != "/usr/local/nginx/html/dist_keycloak" ]; then
    echo "复制前端文件到nginx目录..."
    sudo rm -rf /usr/local/nginx/html/dist_keycloak
    sudo cp -r "$FRONTEND_DIR" /usr/local/nginx/html/dist_keycloak
    echo "✅ 前端文件复制完成"
fi

# 4. 设置正确的权限
echo ""
echo "步骤4: 设置文件权限..."
sudo chown -R nginx:nginx /usr/local/nginx/html/dist_keycloak 2>/dev/null || \
sudo chown -R www-data:www-data /usr/local/nginx/html/dist_keycloak 2>/dev/null || \
sudo chown -R root:root /usr/local/nginx/html/dist_keycloak

sudo chmod -R 644 /usr/local/nginx/html/dist_keycloak
sudo find /usr/local/nginx/html/dist_keycloak -type d -exec chmod 755 {} \;

echo "✅ 权限设置完成"

# 5. 重新加载nginx配置
echo ""
echo "步骤5: 重新加载nginx..."
if sudo nginx -t; then
    sudo nginx -s reload
    echo "✅ nginx配置重新加载成功"
else
    echo "❌ nginx配置测试失败"
    exit 1
fi

# 6. 测试访问
echo ""
echo "步骤6: 测试访问..."
sleep 2

echo "测试HTTPS访问:"
if curl -k -f https://localhost:8089 > /dev/null 2>&1; then
    echo "✅ HTTPS访问正常"
else
    echo "❌ HTTPS访问失败"
fi

echo "测试HTTP重定向:"
REDIRECT_RESULT=$(curl -s -I http://localhost:8088 | grep Location)
if echo "$REDIRECT_RESULT" | grep -q "9082"; then
    echo "✅ HTTP重定向正确: $REDIRECT_RESULT"
else
    echo "❌ HTTP重定向错误: $REDIRECT_RESULT"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "访问地址:"
echo "  HTTPS: https://*************:9082"
echo "  HTTP: http://*************:9083 (应该重定向到HTTPS)"
echo ""
echo "前端文件位置: /usr/local/nginx/html/dist_keycloak"
echo "文件数量: $(find /usr/local/nginx/html/dist_keycloak -type f | wc -l)"
