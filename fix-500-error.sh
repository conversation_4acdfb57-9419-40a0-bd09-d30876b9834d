#!/bin/bash

# 修复500错误和连接问题

set -e

echo "=== 修复nginx 500错误 ==="

# 1. 停止nginx
echo "步骤1: 停止nginx..."
sudo nginx -s stop 2>/dev/null || sudo pkill nginx 2>/dev/null || true
sleep 2

# 2. 检查并创建必要目录
echo "步骤2: 检查目录结构..."

# 创建日志目录
sudo mkdir -p /var/log/nginx
sudo mkdir -p /usr/local/nginx/logs

# 创建SSL目录
sudo mkdir -p /usr/local/nginx/ssl

# 创建前端目录
sudo mkdir -p /usr/local/nginx/html/dist_keycloak

# 3. 检查前端文件
echo "步骤3: 检查前端文件..."
if [ ! -f "/usr/local/nginx/html/dist_keycloak/index.html" ]; then
    echo "创建临时index.html..."
    sudo tee /usr/local/nginx/html/dist_keycloak/index.html > /dev/null << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XinHe Workbench</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            color: #28a745;
            font-size: 18px;
            margin-bottom: 20px;
        }
        .info {
            color: #6c757d;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 XinHe Workbench</h1>
        <div class="status">✅ 服务器运行正常</div>
        <div class="info">时间: <span id="time"></span></div>
        <div class="info">协议: <span id="protocol"></span></div>
        <div class="info">端口: <span id="port"></span></div>
        <div class="info">状态: 等待前端应用部署...</div>
    </div>
    
    <script>
        document.getElementById('time').textContent = new Date().toLocaleString();
        document.getElementById('protocol').textContent = location.protocol;
        document.getElementById('port').textContent = location.port || (location.protocol === 'https:' ? '443' : '80');
        
        // 每5秒检查一次是否有真正的应用
        setInterval(() => {
            fetch('/api/health').then(response => {
                if (response.ok) {
                    window.location.reload();
                }
            }).catch(() => {
                // API还未就绪
            });
        }, 5000);
    </script>
</body>
</html>
EOF
    echo "✅ 创建了临时前端页面"
fi

# 4. 检查SSL证书
echo "步骤4: 检查SSL证书..."
if [ ! -f "/usr/local/nginx/ssl/server.crt" ] || [ ! -f "/usr/local/nginx/ssl/server.key" ]; then
    echo "生成SSL证书..."
    chmod +x generate-ssl-simple.sh
    ./generate-ssl-simple.sh
fi

# 5. 创建简化的nginx配置
echo "步骤5: 创建简化nginx配置..."
sudo tee /usr/local/nginx/conf/nginx.conf > /dev/null << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    
    # 日志配置
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # 基本设置
    sendfile on;
    keepalive_timeout 65;
    
    # HTTP服务器 (重定向到HTTPS)
    server {
        listen 8088;
        server_name *************;
        return 301 https://$server_name:9082$request_uri;
    }
    
    # HTTPS服务器
    server {
        listen 8089 ssl;
        server_name *************;
        
        # SSL配置
        ssl_certificate /usr/local/nginx/ssl/server.crt;
        ssl_certificate_key /usr/local/nginx/ssl/server.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        
        # 前端文件
        location / {
            root /usr/local/nginx/html/dist_keycloak;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
        
        # API代理 (如果后端可用)
        location /api/ {
            proxy_pass http://*************:9084/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            
            # 如果后端不可用，返回503
            proxy_intercept_errors on;
            error_page 502 503 504 = @api_fallback;
        }
        
        # API后备处理
        location @api_fallback {
            add_header Content-Type application/json;
            return 503 '{"error": "Backend service unavailable", "message": "Please check if backend service is running on port 9084"}';
        }
        
        # 健康检查
        location /health {
            add_header Content-Type application/json;
            return 200 '{"status": "ok", "service": "nginx", "timestamp": "$time_iso8601"}';
        }
    }
}
EOF

# 6. 测试配置
echo "步骤6: 测试nginx配置..."
if sudo nginx -t; then
    echo "✅ nginx配置测试通过"
else
    echo "❌ nginx配置测试失败"
    exit 1
fi

# 7. 启动nginx
echo "步骤7: 启动nginx..."
sudo nginx

# 8. 等待启动
echo "步骤8: 等待服务启动..."
sleep 3

# 9. 测试连接
echo "步骤9: 测试连接..."
echo "测试HTTP:"
curl -I http://localhost:8088 2>/dev/null | head -1 || echo "HTTP测试失败"

echo "测试HTTPS:"
curl -I -k https://localhost:8089 2>/dev/null | head -1 || echo "HTTPS测试失败"

echo ""
echo "=== 修复完成 ==="
echo ""
echo "访问地址:"
echo "  HTTPS: https://*************:9082"
echo "  HTTP: http://*************:9083 (重定向到HTTPS)"
echo ""
echo "如果仍有问题，请运行诊断脚本:"
echo "  chmod +x diagnose-nginx.sh && ./diagnose-nginx.sh"
