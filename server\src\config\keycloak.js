const Keycloak = require("keycloak-connect");

let keycloak;

const keycloakConfig = {
  "auth-server-url": process.env.KEYCLOAK_URL || "https://111.13.109.67:9088",
  realm: process.env.KEYCLOAK_REALM || "dev_xh_key",
  resource: process.env.KEYCLOAK_CLIENT_ID || "sulei_01",
  credentials: {
    secret: process.env.KEYCLOAK_CLIENT_SECRET,
  },
  "ssl-required": "external",
  "confidential-port": 9082, // HTTPS端口
  "bearer-only": true, //只需要token则设置为true
};

function initKeycloak(app) {
  if (keycloak) {
    console.warn("Keycloak已经初始化");
    return keycloak;
  }

  // console.log("初始化Keycloak...");
  // console.log("Keycloak配置:", {
  //   ...keycloakConfig,
  //   credentials: { secret: "***" }, // 隐藏敏感信息
  // });

  keycloak = new Keycloak({}, keycloakConfig);

  // 安装Keycloak中间件
  app.use(
    keycloak.middleware({
      logout: "/api/auth/logout",
      admin: "/",
    })
  );

  console.log("✅ Keycloak初始化完成");
  return keycloak;
}

function getKeycloak() {
  if (!keycloak) {
    throw new Error("Keycloak尚未初始化");
  }
  return keycloak;
}

module.exports = {
  initKeycloak,
  getKeycloak,
  keycloakConfig,
};
