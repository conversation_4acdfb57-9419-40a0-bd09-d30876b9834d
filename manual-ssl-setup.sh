#!/bin/bash

# 手动SSL证书设置脚本

set -e

echo "=== 手动SSL证书设置 ==="

# 配置变量
DOMAIN="111.13.109.67"
SSL_DIR="/usr/local/nginx/ssl"
CERT_FILE="$SSL_DIR/server.crt"
KEY_FILE="$SSL_DIR/server.key"
CSR_FILE="$SSL_DIR/server.csr"

echo "域名: $DOMAIN"
echo "SSL目录: $SSL_DIR"

# 1. 创建SSL目录
echo "步骤1: 创建SSL目录..."
sudo mkdir -p $SSL_DIR

# 2. 生成私钥
echo "步骤2: 生成私钥..."
sudo openssl genrsa -out $KEY_FILE 2048

# 3. 创建证书扩展配置文件
echo "步骤3: 创建证书配置..."
sudo tee /tmp/cert_extensions.conf > /dev/null << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C=CN
ST=Beijing
L=Beijing
O=Company
OU=IT
CN=$DOMAIN

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
IP.1 = $DOMAIN
EOF

# 4. 生成证书签名请求
echo "步骤4: 生成证书签名请求..."
sudo openssl req -new -key $KEY_FILE -out $CSR_FILE -config /tmp/cert_extensions.conf

# 5. 生成自签名证书
echo "步骤5: 生成自签名证书..."
sudo openssl x509 -req -days 365 -in $CSR_FILE -signkey $KEY_FILE -out $CERT_FILE -extensions v3_req -extfile /tmp/cert_extensions.conf

# 6. 设置权限
echo "步骤6: 设置证书权限..."
sudo chmod 600 $KEY_FILE
sudo chmod 644 $CERT_FILE

# 7. 清理临时文件
sudo rm -f /tmp/cert_extensions.conf $CSR_FILE

# 8. 验证证书
echo "步骤7: 验证证书..."
echo "证书信息:"
sudo openssl x509 -in $CERT_FILE -text -noout | grep -E "(Subject:|Issuer:|Not Before:|Not After:)"
echo ""
echo "SAN扩展:"
sudo openssl x509 -in $CERT_FILE -text -noout | grep -A 1 "Subject Alternative Name" || echo "未找到SAN扩展"

echo ""
echo "=== SSL证书生成完成 ==="
echo "证书文件: $CERT_FILE"
echo "私钥文件: $KEY_FILE"
echo ""
echo "现在可以使用HTTPS配置启动nginx:"
echo "sudo nginx -t && sudo nginx -s reload"
