#!/bin/bash

# SSL错误修复脚本
# 解决 ERR_SSL_KEY_USAGE_INCOMPATIBLE 错误

set -e

echo "=== SSL错误修复脚本 ==="

# 检查当前nginx状态
echo "步骤1: 检查nginx状态..."
if pgrep nginx > /dev/null; then
    echo "✅ nginx正在运行"
    NGINX_RUNNING=true
else
    echo "⚠️  nginx未运行"
    NGINX_RUNNING=false
fi

# 备份当前配置
echo "步骤2: 备份当前配置..."
if [ -f "/usr/local/nginx/conf/nginx.conf" ]; then
    sudo cp /usr/local/nginx/conf/nginx.conf /usr/local/nginx/conf/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份nginx配置"
fi

# 选择修复方案
echo ""
echo "请选择修复方案:"
echo "1) 简化版SSL证书 (推荐)"
echo "2) 增强版SSL证书"
echo "3) 使用HTTP模式 (临时方案)"
echo "4) 检查现有证书"
read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "=== 方案1: 简化版SSL证书 ==="

        # 停止nginx
        if [ "$NGINX_RUNNING" = true ]; then
            echo "停止nginx..."
            sudo nginx -s stop || true
            sleep 2
        fi

        # 删除旧证书
        echo "删除旧证书..."
        sudo rm -f /usr/local/nginx/ssl/server.*

        # 生成新证书
        echo "生成简化版SSL证书..."
        chmod +x generate-ssl-simple.sh
        ./generate-ssl-simple.sh

        # 测试nginx配置
        echo "测试nginx配置..."
        if sudo nginx -t; then
            echo "✅ nginx配置测试通过"
            sudo nginx
            echo "✅ nginx已启动"
        else
            echo "❌ nginx配置测试失败"
            exit 1
        fi
        ;;

    2)
        echo ""
        echo "=== 方案2: 增强版SSL证书 ==="

        # 停止nginx
        if [ "$NGINX_RUNNING" = true ]; then
            echo "停止nginx..."
            sudo nginx -s stop || true
            sleep 2
        fi

        # 删除旧证书
        echo "删除旧证书..."
        sudo rm -f /usr/local/nginx/ssl/server.*

        # 生成新证书
        echo "生成增强版SSL证书..."
        chmod +x generate-ssl-cert-v2.sh
        ./generate-ssl-cert-v2.sh

        # 测试nginx配置
        echo "测试nginx配置..."
        if sudo nginx -t; then
            echo "✅ nginx配置测试通过"
            sudo nginx
            echo "✅ nginx已启动"
        else
            echo "❌ nginx配置测试失败"
            exit 1
        fi
        ;;

    3)
        echo ""
        echo "=== 方案3: 使用HTTP模式 ==="
        
        # 停止nginx
        if [ "$NGINX_RUNNING" = true ]; then
            echo "停止nginx..."
            sudo nginx -s stop || true
            sleep 2
        fi
        
        # 使用HTTP配置
        echo "应用HTTP配置..."
        sudo cp nginx-http.conf /usr/local/nginx/conf/nginx.conf
        
        # 测试nginx配置
        echo "测试nginx配置..."
        if sudo nginx -t; then
            echo "✅ nginx配置测试通过"
            sudo nginx
            echo "✅ nginx已启动"
        else
            echo "❌ nginx配置测试失败"
            exit 1
        fi
        ;;

    4)
        echo ""
        echo "=== 方案4: 检查现有证书 ==="
        
        if [ -f "/usr/local/nginx/ssl/server.crt" ]; then
            echo "证书文件存在，检查详细信息..."
            echo ""
            echo "证书基本信息:"
            sudo openssl x509 -in /usr/local/nginx/ssl/server.crt -text -noout | grep -E "(Subject:|Issuer:|Not Before:|Not After:)"
            echo ""
            echo "密钥用途:"
            sudo openssl x509 -in /usr/local/nginx/ssl/server.crt -text -noout | grep -A 5 "Key Usage" || echo "未找到密钥用途信息"
            echo ""
            echo "扩展密钥用途:"
            sudo openssl x509 -in /usr/local/nginx/ssl/server.crt -text -noout | grep -A 5 "Extended Key Usage" || echo "未找到扩展密钥用途信息"
            echo ""
            echo "SAN扩展:"
            sudo openssl x509 -in /usr/local/nginx/ssl/server.crt -text -noout | grep -A 5 "Subject Alternative Name" || echo "未找到SAN扩展"
            
            # 验证证书和私钥匹配
            if [ -f "/usr/local/nginx/ssl/server.key" ]; then
                echo ""
                echo "验证证书和私钥匹配..."
                CERT_HASH=$(sudo openssl x509 -noout -modulus -in /usr/local/nginx/ssl/server.crt | openssl md5)
                KEY_HASH=$(sudo openssl rsa -noout -modulus -in /usr/local/nginx/ssl/server.key | openssl md5)
                
                if [ "$CERT_HASH" = "$KEY_HASH" ]; then
                    echo "✅ 证书和私钥匹配"
                else
                    echo "❌ 证书和私钥不匹配"
                fi
            fi
        else
            echo "❌ 证书文件不存在: /usr/local/nginx/ssl/server.crt"
            echo "请选择方案1重新生成证书"
        fi
        ;;
        
    *)
        echo "无效选择"
        exit 1
        ;;
esac

# 检查服务状态
echo ""
echo "步骤3: 检查服务状态..."
sleep 3

if curl -k -f https://111.13.109.67:9082 > /dev/null 2>&1; then
    echo "✅ HTTPS服务正常"
elif curl -f http://111.13.109.67:9083 > /dev/null 2>&1; then
    echo "✅ HTTP服务正常"
else
    echo "⚠️  服务可能未正常启动，请检查日志"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "访问地址:"
if [ "$choice" = "2" ]; then
    echo "  HTTP: http://111.13.109.67:9083"
else
    echo "  HTTPS: https://111.13.109.67:9082"
    echo "  HTTP: http://111.13.109.67:9083 (重定向到HTTPS)"
fi
echo ""
echo "如果仍有问题，请查看nginx日志:"
echo "  sudo tail -f /var/log/nginx/error.log"
