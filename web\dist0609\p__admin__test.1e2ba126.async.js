"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[171],{56038:function(ye,q,o){o.r(q),o.d(q,{default:function(){return ge}});var te=o(15009),v=o.n(te),se=o(97857),p=o.n(se),ae=o(99289),A=o.n(ae),le=o(5574),_=o.n(le),ee=o(67294),re=o(71471),f=o(2453),y=o(4393),ue=o(40056),D=o(78957),F=o(83622),l=o(26412),x=o(84226),ie=o(7528);function fe(u,n){return w.apply(this,arguments)}function w(){return w=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/udp/send",_objectSpread({method:"POST"},s||{})));case 1:case"end":return r.stop()}},u)})),w.apply(this,arguments)}function je(u,n){return k.apply(this,arguments)}function k(){return k=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/deploy/configcheck",_objectSpread({method:"POST",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),k.apply(this,arguments)}function De(u,n){return C.apply(this,arguments)}function C(){return C=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/deploy/servicecheck",_objectSpread({method:"POST",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),C.apply(this,arguments)}function Be(u,n){return R.apply(this,arguments)}function R(){return R=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/deploy/servicestart",_objectSpread({method:"POST",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),R.apply(this,arguments)}function Ze(u,n,s){return N.apply(this,arguments)}function N(){return N=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s,t){return _regeneratorRuntime().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return console.log("Base URL:",n),g.abrupt("return",request("/chat",_objectSpread({method:"POST",data:s},t||{})));case 2:case"end":return g.stop()}},u)})),N.apply(this,arguments)}function Ee(u){return O.apply(this,arguments)}function O(){return O=_asyncToGenerator(_regeneratorRuntime().mark(function u(n){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",request("/api/deploy/chat",_objectSpread({method:"POST"},n||{})));case 1:case"end":return t.stop()}},u)})),O.apply(this,arguments)}function Ae(u){return G.apply(this,arguments)}function G(){return G=_asyncToGenerator(_regeneratorRuntime().mark(function u(n){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",request("/api/deploy/startchat",_objectSpread({method:"POST"},n||{})));case 1:case"end":return t.stop()}},u)})),G.apply(this,arguments)}function Fe(u,n){return $.apply(this,arguments)}function $(){return $=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/deploy/service/start",_objectSpread({method:"POST",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),$.apply(this,arguments)}function Ie(u,n){return z.apply(this,arguments)}function z(){return z=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/deploy/service/stop",_objectSpread({method:"POST",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),z.apply(this,arguments)}function ce(u){return U.apply(this,arguments)}function U(){return U=A()(v()().mark(function u(n){return v()().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,x.request)("/api/deploy/service/list",p()({method:"GET"},n||{})));case 1:case"end":return t.stop()}},u)})),U.apply(this,arguments)}function Se(u,n){return M.apply(this,arguments)}function M(){return M=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/deploy/service/get",_objectSpread({method:"GET",params:n},s||{})));case 1:case"end":return r.stop()}},u)})),M.apply(this,arguments)}function xe(u,n){return L.apply(this,arguments)}function L(){return L=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/deploy/service/remove",_objectSpread({method:"DELETE",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),L.apply(this,arguments)}function oe(u,n){return K.apply(this,arguments)}function K(){return K=A()(v()().mark(function u(n,s){return v()().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,x.request)("/api/node/checkIP",p()({method:"POST",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),K.apply(this,arguments)}function Pe(u,n){return J.apply(this,arguments)}function J(){return J=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/node/register",_objectSpread({method:"POST",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),J.apply(this,arguments)}function Te(u){return W.apply(this,arguments)}function W(){return W=_asyncToGenerator(_regeneratorRuntime().mark(function u(n){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",request("/api/node/list",_objectSpread({method:"GET"},n||{})));case 1:case"end":return t.stop()}},u)})),W.apply(this,arguments)}function be(u,n){return H.apply(this,arguments)}function H(){return H=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/node/get",_objectSpread({method:"GET",params:n},s||{})));case 1:case"end":return r.stop()}},u)})),H.apply(this,arguments)}function we(u,n){return Q.apply(this,arguments)}function Q(){return Q=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/node/update",_objectSpread({method:"PUT",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),Q.apply(this,arguments)}function ke(u,n){return V.apply(this,arguments)}function V(){return V=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/node/remove",_objectSpread({method:"DELETE",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),V.apply(this,arguments)}function Ce(u,n){return X.apply(this,arguments)}function X(){return X=_asyncToGenerator(_regeneratorRuntime().mark(function u(n,s){return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",request("/api/node/status",_objectSpread({method:"POST",data:n},s||{})));case 1:case"end":return r.stop()}},u)})),X.apply(this,arguments)}var B=o(66309),P=o(68744),e=o(85893),de=function(){var n=(0,x.useModel)("@@initialState"),s=n.initialState,t=(0,x.useAccess)(),r=function(){var i=A()(v()().mark(function m(){var I;return v()().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:try{I=P.e.getUserInfo(),console.log("Keycloak\u7528\u6237\u4FE1\u606F:",I),console.log("\u5F53\u524D\u7528\u6237\u72B6\u6001:",s==null?void 0:s.currentUser),console.log("\u6743\u9650\u72B6\u6001:",t),f.ZP.success("\u7528\u6237\u4FE1\u606F\u5DF2\u5237\u65B0\uFF0C\u8BF7\u67E5\u770B\u63A7\u5236\u53F0")}catch(Y){console.error("\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25:",Y),f.ZP.error("\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25")}case 1:case"end":return T.stop()}},m)}));return function(){return i.apply(this,arguments)}}(),g=function(){var m=P.e.getToken();if(console.log("\u5F53\u524DToken:",m),m)try{var I=JSON.parse(atob(m.split(".")[1]));console.log("Token\u5185\u5BB9:",I),f.ZP.success("Token\u4FE1\u606F\u5DF2\u8F93\u51FA\u5230\u63A7\u5236\u53F0")}catch(b){console.error("Token\u89E3\u6790\u5931\u8D25:",b),f.ZP.error("Token\u89E3\u6790\u5931\u8D25")}else f.ZP.warning("\u672A\u627E\u5230Token")},c=s==null?void 0:s.currentUser,d=P.e.getUserInfo();return(0,e.jsx)(y.Z,{title:"\u7528\u6237\u6743\u9650\u8C03\u8BD5\u4FE1\u606F",style:{margin:"16px"},children:(0,e.jsxs)(D.Z,{direction:"vertical",size:"large",style:{width:"100%"},children:[(0,e.jsxs)(D.Z,{children:[(0,e.jsx)(F.ZP,{type:"primary",onClick:r,children:"\u5237\u65B0\u7528\u6237\u4FE1\u606F"}),(0,e.jsx)(F.ZP,{onClick:g,children:"\u68C0\u67E5Token"})]}),(0,e.jsx)(y.Z,{size:"small",title:"\u5F53\u524D\u7528\u6237\u72B6\u6001 (initialState)",type:"inner",children:(0,e.jsxs)(l.Z,{column:1,size:"small",children:[(0,e.jsx)(l.Z.Item,{label:"\u7528\u6237ID",children:(c==null?void 0:c.userid)||"\u672A\u8BBE\u7F6E"}),(0,e.jsx)(l.Z.Item,{label:"\u7528\u6237\u540D",children:(c==null?void 0:c.username)||"\u672A\u8BBE\u7F6E"}),(0,e.jsx)(l.Z.Item,{label:"\u59D3\u540D",children:(c==null?void 0:c.name)||"\u672A\u8BBE\u7F6E"}),(0,e.jsx)(l.Z.Item,{label:"\u90AE\u7BB1",children:(c==null?void 0:c.email)||"\u672A\u8BBE\u7F6E"}),(0,e.jsx)(l.Z.Item,{label:"\u8BBF\u95EE\u7EA7\u522B",children:(0,e.jsx)(B.Z,{color:(c==null?void 0:c.access)==="owner"?"red":"blue",children:(c==null?void 0:c.access)||"\u672A\u8BBE\u7F6E"})}),(0,e.jsx)(l.Z.Item,{label:"\u89D2\u8272\u5217\u8868",children:c!=null&&c.roles?(0,e.jsx)(D.Z,{children:c.roles.map(function(i){return(0,e.jsx)(B.Z,{color:"green",children:i},i)})}):"\u672A\u8BBE\u7F6E"})]})}),(0,e.jsx)(y.Z,{size:"small",title:"Keycloak\u7528\u6237\u4FE1\u606F",type:"inner",children:(0,e.jsxs)(l.Z,{column:1,size:"small",children:[(0,e.jsx)(l.Z.Item,{label:"\u8BA4\u8BC1\u72B6\u6001",children:(0,e.jsx)(B.Z,{color:P.e.isAuthenticated()?"green":"red",children:P.e.isAuthenticated()?"\u5DF2\u8BA4\u8BC1":"\u672A\u8BA4\u8BC1"})}),(0,e.jsx)(l.Z.Item,{label:"\u7528\u6237ID",children:(d==null?void 0:d.id)||"\u672A\u83B7\u53D6"}),(0,e.jsx)(l.Z.Item,{label:"\u7528\u6237\u540D",children:(d==null?void 0:d.username)||"\u672A\u83B7\u53D6"}),(0,e.jsx)(l.Z.Item,{label:"\u59D3\u540D",children:(d==null?void 0:d.name)||"\u672A\u83B7\u53D6"}),(0,e.jsx)(l.Z.Item,{label:"\u90AE\u7BB1",children:(d==null?void 0:d.email)||"\u672A\u83B7\u53D6"}),(0,e.jsx)(l.Z.Item,{label:"\u89D2\u8272\u5217\u8868",children:d!=null&&d.roles?(0,e.jsx)(D.Z,{children:d.roles.map(function(i){return(0,e.jsx)(B.Z,{color:"blue",children:i},i)})}):"\u672A\u83B7\u53D6"})]})}),(0,e.jsx)(y.Z,{size:"small",title:"\u6743\u9650\u68C0\u67E5\u7ED3\u679C",type:"inner",children:(0,e.jsxs)(l.Z,{column:1,size:"small",children:[(0,e.jsx)(l.Z.Item,{label:"\u662F\u5426\u7BA1\u7406\u5458",children:(0,e.jsx)(B.Z,{color:t.isAdmin?"green":"red",children:t.isAdmin?"\u662F":"\u5426"})}),(0,e.jsx)(l.Z.Item,{label:"\u53EF\u7BA1\u7406\u9879\u76EE",children:(0,e.jsx)(B.Z,{color:t.canManageProjects?"green":"red",children:t.canManageProjects?"\u662F":"\u5426"})}),(0,e.jsx)(l.Z.Item,{label:"\u53EF\u7BA1\u7406\u90E8\u7F72",children:(0,e.jsx)(B.Z,{color:t.canManageDeploy?"green":"red",children:t.canManageDeploy?"\u662F":"\u5426"})}),(0,e.jsx)(l.Z.Item,{label:"\u53EF\u7BA1\u7406\u8282\u70B9",children:(0,e.jsx)(B.Z,{color:t.canManageNodes?"green":"red",children:t.canManageNodes?"\u662F":"\u5426"})})]})}),(0,e.jsx)(y.Z,{size:"small",title:"\u8C03\u8BD5\u8BF4\u660E",type:"inner",children:(0,e.jsxs)("ul",{children:[(0,e.jsx)("li",{children:'\u68C0\u67E5"\u5F53\u524D\u7528\u6237\u72B6\u6001"\u4E2D\u7684\u89D2\u8272\u5217\u8868\u662F\u5426\u5305\u542B owner/admin/maintainer'}),(0,e.jsx)("li",{children:'\u68C0\u67E5"\u6743\u9650\u68C0\u67E5\u7ED3\u679C"\u4E2D\u7684"\u662F\u5426\u7BA1\u7406\u5458"\u662F\u5426\u4E3A"\u662F"'}),(0,e.jsx)("li",{children:"\u5982\u679C\u89D2\u8272\u4FE1\u606F\u4E0D\u6B63\u786E\uFF0C\u8BF7\u68C0\u67E5Keycloak\u4E2D\u7684\u7528\u6237\u89D2\u8272\u914D\u7F6E"}),(0,e.jsx)("li",{children:'\u70B9\u51FB"\u5237\u65B0\u7528\u6237\u4FE1\u606F"\u6309\u94AE\u67E5\u770B\u63A7\u5236\u53F0\u8F93\u51FA\u7684\u8BE6\u7EC6\u4FE1\u606F'}),(0,e.jsx)("li",{children:'\u70B9\u51FB"\u68C0\u67E5Token"\u6309\u94AE\u67E5\u770B\u5F53\u524D\u7684\u8BA4\u8BC1Token\u5185\u5BB9'})]})})]})})},pe=de,he=re.Z.Title,h=re.Z.Text,me=function(){var n=(0,x.useAccess)(),s=(0,ee.useState)(!1),t=_()(s,2),r=t[0],g=t[1],c=(0,ee.useState)({}),d=_()(c,2),i=d[0],m=d[1],I=n.isAdmin,b=function(){var S=A()(v()().mark(function Z(){var E;return v()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return g(!0),a.prev=1,a.next=4,(0,ie.hW)();case 4:E=a.sent,m(function(j){return p()(p()({},j),{},{devops:{success:!0,data:E,message:"\u83B7\u53D6\u9879\u76EE\u5217\u8868\u6210\u529F"}})}),f.ZP.success("DevOps API \u6D4B\u8BD5\u6210\u529F"),a.next=13;break;case 9:a.prev=9,a.t0=a.catch(1),m(function(j){return p()(p()({},j),{},{devops:{success:!1,error:a.t0.message,message:"DevOps API \u6D4B\u8BD5\u5931\u8D25"}})}),f.ZP.error("DevOps API \u6D4B\u8BD5\u5931\u8D25");case 13:return a.prev=13,g(!1),a.finish(13);case 16:case"end":return a.stop()}},Z,null,[[1,9,13,16]])}));return function(){return S.apply(this,arguments)}}(),T=function(){var S=A()(v()().mark(function Z(){var E;return v()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return g(!0),a.prev=1,a.next=4,ce();case 4:E=a.sent,m(function(j){return p()(p()({},j),{},{deploy:{success:!0,data:E,message:"\u83B7\u53D6\u670D\u52A1\u5217\u8868\u6210\u529F"}})}),f.ZP.success("Deploy API \u6D4B\u8BD5\u6210\u529F"),a.next=13;break;case 9:a.prev=9,a.t0=a.catch(1),m(function(j){return p()(p()({},j),{},{deploy:{success:!1,error:a.t0.message,message:"Deploy API \u6D4B\u8BD5\u5931\u8D25"}})}),f.ZP.error("Deploy API \u6D4B\u8BD5\u5931\u8D25");case 13:return a.prev=13,g(!1),a.finish(13);case 16:case"end":return a.stop()}},Z,null,[[1,9,13,16]])}));return function(){return S.apply(this,arguments)}}(),Y=function(){var S=A()(v()().mark(function Z(){var E;return v()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return g(!0),a.prev=1,a.next=4,oe({ip:"127.0.0.1"});case 4:E=a.sent,m(function(j){return p()(p()({},j),{},{node:{success:!0,data:E,message:"\u8282\u70B9IP\u68C0\u67E5\u6210\u529F"}})}),f.ZP.success("Node API \u6D4B\u8BD5\u6210\u529F"),a.next=13;break;case 9:a.prev=9,a.t0=a.catch(1),m(function(j){return p()(p()({},j),{},{node:{success:!1,error:a.t0.message,message:"Node API \u6D4B\u8BD5\u5931\u8D25"}})}),f.ZP.error("Node API \u6D4B\u8BD5\u5931\u8D25");case 13:return a.prev=13,g(!1),a.finish(13);case 16:case"end":return a.stop()}},Z,null,[[1,9,13,16]])}));return function(){return S.apply(this,arguments)}}(),ve=function(){m({})};return I?(0,e.jsxs)("div",{style:{padding:"24px"},children:[(0,e.jsx)(he,{level:2,children:"\u7BA1\u7406\u5458API\u6D4B\u8BD5\u9875\u9762"}),(0,e.jsx)(ue.Z,{message:"\u6D4B\u8BD5\u8BF4\u660E",description:"\u6B64\u9875\u9762\u7528\u4E8E\u6D4B\u8BD5\u7BA1\u7406\u5458\u6743\u9650\u7684API\u63A5\u53E3\u3002\u6240\u6709\u8BF7\u6C42\u90FD\u4F1A\u81EA\u52A8\u6DFB\u52A0Keycloak\u8BA4\u8BC1token\u3002",type:"info",showIcon:!0,style:{marginBottom:"24px"}}),(0,e.jsx)(pe,{}),(0,e.jsxs)(D.Z,{direction:"vertical",size:"large",style:{width:"100%"},children:[(0,e.jsx)(y.Z,{title:"API\u6D4B\u8BD5",size:"small",children:(0,e.jsxs)(D.Z,{wrap:!0,children:[(0,e.jsx)(F.ZP,{type:"primary",onClick:b,loading:r,children:"\u6D4B\u8BD5 DevOps API"}),(0,e.jsx)(F.ZP,{type:"primary",onClick:T,loading:r,children:"\u6D4B\u8BD5 Deploy API"}),(0,e.jsx)(F.ZP,{type:"primary",onClick:Y,loading:r,children:"\u6D4B\u8BD5 Node API"}),(0,e.jsx)(F.ZP,{onClick:ve,children:"\u6E05\u9664\u7ED3\u679C"})]})}),Object.keys(i).length>0&&(0,e.jsx)(y.Z,{title:"\u6D4B\u8BD5\u7ED3\u679C",size:"small",children:(0,e.jsxs)(D.Z,{direction:"vertical",size:"middle",style:{width:"100%"},children:[i.devops&&(0,e.jsx)(y.Z,{size:"small",title:"DevOps API (/api/devops)",type:"inner",children:(0,e.jsxs)(l.Z,{column:1,size:"small",children:[(0,e.jsx)(l.Z.Item,{label:"\u72B6\u6001",children:(0,e.jsx)(h,{type:i.devops.success?"success":"danger",children:i.devops.success?"\u6210\u529F":"\u5931\u8D25"})}),(0,e.jsx)(l.Z.Item,{label:"\u6D88\u606F",children:i.devops.message}),i.devops.error&&(0,e.jsx)(l.Z.Item,{label:"\u9519\u8BEF",children:(0,e.jsx)(h,{type:"danger",children:i.devops.error})}),i.devops.data&&(0,e.jsx)(l.Z.Item,{label:"\u6570\u636E",children:(0,e.jsx)(h,{code:!0,children:JSON.stringify(i.devops.data,null,2)})})]})}),i.deploy&&(0,e.jsx)(y.Z,{size:"small",title:"Deploy API (/api/deploy)",type:"inner",children:(0,e.jsxs)(l.Z,{column:1,size:"small",children:[(0,e.jsx)(l.Z.Item,{label:"\u72B6\u6001",children:(0,e.jsx)(h,{type:i.deploy.success?"success":"danger",children:i.deploy.success?"\u6210\u529F":"\u5931\u8D25"})}),(0,e.jsx)(l.Z.Item,{label:"\u6D88\u606F",children:i.deploy.message}),i.deploy.error&&(0,e.jsx)(l.Z.Item,{label:"\u9519\u8BEF",children:(0,e.jsx)(h,{type:"danger",children:i.deploy.error})}),i.deploy.data&&(0,e.jsx)(l.Z.Item,{label:"\u6570\u636E",children:(0,e.jsx)(h,{code:!0,children:JSON.stringify(i.deploy.data,null,2)})})]})}),i.node&&(0,e.jsx)(y.Z,{size:"small",title:"Node API (/api/node)",type:"inner",children:(0,e.jsxs)(l.Z,{column:1,size:"small",children:[(0,e.jsx)(l.Z.Item,{label:"\u72B6\u6001",children:(0,e.jsx)(h,{type:i.node.success?"success":"danger",children:i.node.success?"\u6210\u529F":"\u5931\u8D25"})}),(0,e.jsx)(l.Z.Item,{label:"\u6D88\u606F",children:i.node.message}),i.node.error&&(0,e.jsx)(l.Z.Item,{label:"\u9519\u8BEF",children:(0,e.jsx)(h,{type:"danger",children:i.node.error})}),i.node.data&&(0,e.jsx)(l.Z.Item,{label:"\u6570\u636E",children:(0,e.jsx)(h,{code:!0,children:JSON.stringify(i.node.data,null,2)})})]})})]})}),(0,e.jsx)(y.Z,{title:"\u4F7F\u7528\u8BF4\u660E",size:"small",children:(0,e.jsxs)(D.Z,{direction:"vertical",children:[(0,e.jsx)(h,{children:"1. \u786E\u4FDD\u60A8\u7684\u8D26\u6237\u5177\u6709admin\u89D2\u8272"}),(0,e.jsx)(h,{children:"2. \u70B9\u51FB\u6D4B\u8BD5\u6309\u94AE\u4F1A\u8C03\u7528\u76F8\u5E94\u7684\u7BA1\u7406\u5458API"}),(0,e.jsx)(h,{children:"3. \u8BF7\u6C42\u4F1A\u81EA\u52A8\u6DFB\u52A0Keycloak\u8BA4\u8BC1token"}),(0,e.jsx)(h,{children:"4. \u5982\u679C\u8FD4\u56DE403\u9519\u8BEF\uFF0C\u8BF4\u660E\u6743\u9650\u4E0D\u8DB3"}),(0,e.jsx)(h,{children:"5. \u5982\u679C\u8FD4\u56DE401\u9519\u8BEF\uFF0C\u8BF4\u660E\u8BA4\u8BC1\u5931\u8D25\uFF0C\u9700\u8981\u91CD\u65B0\u767B\u5F55"})]})})]})]}):(0,e.jsx)(y.Z,{title:"\u7BA1\u7406\u5458API\u6D4B\u8BD5",children:(0,e.jsx)(ue.Z,{message:"\u6743\u9650\u4E0D\u8DB3",description:"\u6B64\u9875\u9762\u9700\u8981\u7BA1\u7406\u5458\u6743\u9650\u624D\u80FD\u8BBF\u95EE\u3002\u8BF7\u786E\u4FDD\u60A8\u7684\u8D26\u6237\u5177\u6709admin\u89D2\u8272\u3002",type:"warning",showIcon:!0})})},ge=me}}]);
