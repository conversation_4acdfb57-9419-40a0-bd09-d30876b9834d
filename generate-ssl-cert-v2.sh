#!/bin/bash

# 增强版SSL证书生成脚本
# 解决 ERR_SSL_KEY_USAGE_INCOMPATIBLE 错误

set -e

# 配置变量
DOMAIN="*************"
SSL_DIR="/usr/local/nginx/ssl"
CERT_FILE="$SSL_DIR/server.crt"
KEY_FILE="$SSL_DIR/server.key"
CSR_FILE="$SSL_DIR/server.csr"

echo "=== 增强版SSL证书生成 ==="
echo "域名: $DOMAIN"
echo "SSL目录: $SSL_DIR"

# 1. 创建SSL目录
echo "步骤1: 创建SSL目录..."
 mkdir -p $SSL_DIR

# 2. 生成强私钥 (RSA 4096位)
echo "步骤2: 生成4096位RSA私钥..."
 openssl genrsa -out $KEY_FILE 4096

# 3. 创建详细的证书配置文件
echo "步骤3: 创建证书配置文件..."
 tee /tmp/openssl.conf > /dev/null << EOF
[req]
default_bits = 4096
prompt = no
distinguished_name = req_distinguished_name
req_extensions = v3_req

[req_distinguished_name]
C=CN
ST=Beijing
L=Beijing
O=XinHe Company
OU=IT Department
CN=$DOMAIN
emailAddress=<EMAIL>

[v3_req]
basicConstraints = critical, CA:FALSE
keyUsage = critical, digitalSignature, keyEncipherment, keyAgreement, nonRepudiation
extendedKeyUsage = critical, serverAuth, clientAuth
subjectAltName = @alt_names
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer:always
issuerAltName = issuer:copy

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = localhost
DNS.3 = *.*************
IP.1 = $DOMAIN
IP.2 = 127.0.0.1
IP.3 = ::1

[v3_ca]
basicConstraints = critical, CA:TRUE
keyUsage = critical, keyCertSign, cRLSign, digitalSignature
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer:always
EOF

# 4. 生成证书签名请求
echo "步骤4: 生成证书签名请求..."
 openssl req -new -key $KEY_FILE -out $CSR_FILE -config /tmp/openssl.conf

# 5. 生成自签名证书 (有效期2年)
echo "步骤5: 生成自签名证书..."
 openssl x509 -req -days 730 -in $CSR_FILE -signkey $KEY_FILE -out $CERT_FILE \
    -extensions v3_req -extfile /tmp/openssl.conf

# 6. 验证证书
echo "步骤6: 验证证书..."
if  openssl x509 -in $CERT_FILE -text -noout > /dev/null 2>&1; then
    echo "✅ 证书格式验证通过"
else
    echo "❌ 证书格式验证失败"
    exit 1
fi

# 7. 检查密钥用途
echo "步骤7: 检查密钥用途..."
echo "证书详细信息:"
 openssl x509 -in $CERT_FILE -text -noout | grep -A 10 "X509v3 extensions"

# 8. 设置权限
echo "步骤8: 设置证书权限..."
 chmod 600 $KEY_FILE
 chmod 644 $CERT_FILE
 chown root:root $KEY_FILE $CERT_FILE

# 9. 清理临时文件
echo "步骤9: 清理临时文件..."
 rm -f /tmp/openssl.conf $CSR_FILE

# 10. 验证证书和私钥匹配
echo "步骤10: 验证证书和私钥匹配..."
CERT_HASH=$( openssl x509 -noout -modulus -in $CERT_FILE | openssl md5)
KEY_HASH=$( openssl rsa -noout -modulus -in $KEY_FILE | openssl md5)

if [ "$CERT_HASH" = "$KEY_HASH" ]; then
    echo "✅ 证书和私钥匹配"
else
    echo "❌ 证书和私钥不匹配"
    exit 1
fi

echo ""
echo "=== SSL证书生成完成 ==="
echo "证书文件: $CERT_FILE"
echo "私钥文件: $KEY_FILE"
echo ""
echo "证书信息:"
 openssl x509 -in $CERT_FILE -text -noout | grep -E "(Subject:|Issuer:|Not Before:|Not After:|DNS:|IP Address:)"
echo ""
echo "密钥用途:"
 openssl x509 -in $CERT_FILE -text -noout | grep -A 3 "Key Usage"
echo ""
echo "扩展密钥用途:"
 openssl x509 -in $CERT_FILE -text -noout | grep -A 3 "Extended Key Usage"
echo ""
echo "现在可以重启nginx:"
echo " nginx -t &&  nginx -s reload"
