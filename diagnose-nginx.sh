#!/bin/bash

# nginx诊断脚本

echo "=== nginx诊断报告 ==="

# 1. 检查nginx进程
echo "1. nginx进程状态:"
if pgrep nginx > /dev/null; then
    echo "✅ nginx正在运行"
    ps aux | grep nginx | grep -v grep
else
    echo "❌ nginx未运行"
fi

echo ""

# 2. 检查端口监听
echo "2. 端口监听状态:"
echo "检查8088端口:"
netstat -tlnp | grep :8088 || echo "❌ 8088端口未监听"
echo "检查8089端口:"
netstat -tlnp | grep :8089 || echo "❌ 8089端口未监听"

echo ""

# 3. 检查nginx配置
echo "3. nginx配置测试:"
if sudo nginx -t; then
    echo "✅ nginx配置语法正确"
else
    echo "❌ nginx配置有错误"
fi

echo ""

# 4. 检查SSL证书
echo "4. SSL证书状态:"
if [ -f "/usr/local/nginx/ssl/server.crt" ]; then
    echo "✅ 证书文件存在"
    echo "证书信息:"
    sudo openssl x509 -in /usr/local/nginx/ssl/server.crt -noout -dates
else
    echo "❌ 证书文件不存在"
fi

if [ -f "/usr/local/nginx/ssl/server.key" ]; then
    echo "✅ 私钥文件存在"
else
    echo "❌ 私钥文件不存在"
fi

echo ""

# 5. 检查前端文件
echo "5. 前端文件状态:"
if [ -d "/usr/local/nginx/html/dist_keycloak" ]; then
    echo "✅ 前端目录存在"
    echo "文件数量: $(find /usr/local/nginx/html/dist_keycloak -type f | wc -l)"
    echo "主要文件:"
    ls -la /usr/local/nginx/html/dist_keycloak/ | head -10
else
    echo "❌ 前端目录不存在: /usr/local/nginx/html/dist_keycloak"
fi

echo ""

# 6. 检查nginx错误日志
echo "6. nginx错误日志 (最近10行):"
if [ -f "/var/log/nginx/error.log" ]; then
    sudo tail -10 /var/log/nginx/error.log
elif [ -f "/usr/local/nginx/logs/error.log" ]; then
    sudo tail -10 /usr/local/nginx/logs/error.log
else
    echo "未找到nginx错误日志文件"
fi

echo ""

# 7. 检查nginx访问日志
echo "7. nginx访问日志 (最近5行):"
if [ -f "/var/log/nginx/access.log" ]; then
    sudo tail -5 /var/log/nginx/access.log
elif [ -f "/usr/local/nginx/logs/access.log" ]; then
    sudo tail -5 /usr/local/nginx/logs/access.log
else
    echo "未找到nginx访问日志文件"
fi

echo ""

# 8. 检查防火墙状态
echo "8. 防火墙状态:"
if command -v firewall-cmd &> /dev/null; then
    echo "firewalld状态:"
    sudo firewall-cmd --list-ports | grep -E "(8088|8089|9082|9083)" || echo "相关端口未开放"
elif command -v ufw &> /dev/null; then
    echo "ufw状态:"
    sudo ufw status | grep -E "(8088|8089|9082|9083)" || echo "相关端口未开放"
else
    echo "未检测到防火墙管理工具"
fi

echo ""

# 9. 测试本地连接
echo "9. 本地连接测试:"
echo "测试HTTP 8088:"
curl -I http://localhost:8088 2>/dev/null | head -1 || echo "❌ HTTP 8088连接失败"

echo "测试HTTPS 8089:"
curl -I -k https://localhost:8089 2>/dev/null | head -1 || echo "❌ HTTPS 8089连接失败"

echo ""
echo "=== 诊断完成 ==="
