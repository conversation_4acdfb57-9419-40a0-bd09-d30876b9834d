#!/bin/bash

# 完整修复404错误

set -e

echo "=== 完整修复404错误 ==="

# 1. 获取nginx信息
NGINX_PREFIX=$(nginx -V 2>&1 | grep -o 'prefix=[^ ]*' | cut -d= -f2)
echo "nginx prefix: $NGINX_PREFIX"

# 2. 确定正确的前端文件路径
echo ""
echo "步骤1: 确定前端文件位置..."

FRONTEND_SOURCE=""
FRONTEND_TARGET="$NGINX_PREFIX/html/dist_keycloak"

# 检查可能的源文件位置
if [ -d "web/dist" ] && [ -f "web/dist/index.html" ]; then
    FRONTEND_SOURCE="web/dist"
    echo "✅ 找到前端源文件: $FRONTEND_SOURCE"
elif [ -d "web/dist_keycloak" ] && [ -f "web/dist_keycloak/index.html" ]; then
    FRONTEND_SOURCE="web/dist_keycloak"
    echo "✅ 找到前端源文件: $FRONTEND_SOURCE"
else
    echo "❌ 未找到前端源文件，需要构建前端"
    
    if [ -d "web" ] && [ -f "web/package.json" ]; then
        echo "构建前端应用..."
        cd web
        
        echo "安装依赖..."
        npm install
        
        echo "构建应用..."
        npm run build
        
        if [ -d "dist" ]; then
            FRONTEND_SOURCE="../web/dist"
            echo "✅ 前端构建成功"
        else
            echo "❌ 前端构建失败"
            cd ..
            exit 1
        fi
        cd ..
    else
        echo "创建临时前端文件..."
        mkdir -p temp_frontend
        cat > temp_frontend/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XinHe Workbench</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        .logo { font-size: 48px; margin-bottom: 20px; }
        .title { font-size: 32px; color: #333; margin-bottom: 10px; }
        .subtitle { color: #666; margin-bottom: 30px; font-size: 18px; }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: left;
            margin: 20px 0;
        }
        .info-item {
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
        }
        .btn:hover { background: #5a6fd8; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀</div>
        <h1 class="title">XinHe Workbench</h1>
        <p class="subtitle">芯合跨架构智算软件工厂</p>
        
        <div class="status">
            <strong>✅ 404错误已修复！</strong><br>
            nginx服务器现在可以正确提供静态文件服务
        </div>
        
        <div class="info">
            <div class="info-item"><strong>访问时间:</strong> <span id="time"></span></div>
            <div class="info-item"><strong>访问协议:</strong> <span id="protocol"></span></div>
            <div class="info-item"><strong>访问端口:</strong> <span id="port"></span></div>
            <div class="info-item"><strong>服务状态:</strong> <span class="success">正常运行</span></div>
            <div class="info-item"><strong>前端状态:</strong> <span class="warning">等待应用部署</span></div>
        </div>
        
        <div>
            <a href="/api/health" class="btn">检查API状态</a>
            <button onclick="checkApp()" class="btn">检查应用</button>
            <button onclick="location.reload()" class="btn">刷新页面</button>
        </div>
        
        <div style="margin-top: 30px; font-size: 14px; color: #666;">
            <p>如果您看到这个页面，说明nginx配置正确，SSL证书正常。</p>
            <p>请部署您的前端应用文件到正确的目录。</p>
        </div>
    </div>
    
    <script>
        function updateInfo() {
            document.getElementById('time').textContent = new Date().toLocaleString('zh-CN');
            document.getElementById('protocol').textContent = location.protocol;
            document.getElementById('port').textContent = location.port || (location.protocol === 'https:' ? '443' : '80');
        }
        
        function checkApp() {
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    alert('API状态: ' + JSON.stringify(data, null, 2));
                })
                .catch(error => {
                    alert('API不可用: ' + error.message);
                });
        }
        
        updateInfo();
        setInterval(updateInfo, 1000);
    </script>
</body>
</html>
EOF
        FRONTEND_SOURCE="temp_frontend"
        echo "✅ 创建了临时前端文件"
    fi
fi

# 3. 复制前端文件
echo ""
echo "步骤2: 部署前端文件..."
echo "源目录: $FRONTEND_SOURCE"
echo "目标目录: $FRONTEND_TARGET"

# 创建目标目录
sudo mkdir -p "$FRONTEND_TARGET"

# 复制文件
sudo cp -r "$FRONTEND_SOURCE"/* "$FRONTEND_TARGET/"

# 设置权限
sudo chown -R root:root "$FRONTEND_TARGET"
sudo chmod -R 644 "$FRONTEND_TARGET"
sudo find "$FRONTEND_TARGET" -type d -exec chmod 755 {} \;

echo "✅ 前端文件部署完成"
echo "文件数量: $(find "$FRONTEND_TARGET" -type f | wc -l)"

# 4. 修正nginx配置中的root路径
echo ""
echo "步骤3: 修正nginx配置..."

# 创建正确的nginx配置
sudo tee /usr/local/nginx/conf/nginx.conf > /dev/null << EOF
events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    
    # 日志配置
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # 基本设置
    sendfile on;
    keepalive_timeout 65;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HTTP服务器 (重定向到HTTPS)
    server {
        listen 8088;
        server_name *************;
        return 301 https://\$server_name:9082\$request_uri;
    }
    
    # HTTPS服务器
    server {
        listen 8089 ssl http2;
        server_name *************;
        
        # SSL证书配置
        ssl_certificate $NGINX_PREFIX/ssl/server.crt;
        ssl_certificate_key $NGINX_PREFIX/ssl/server.key;
        
        # 安全头设置
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        # 前端静态文件
        location / {
            root $NGINX_PREFIX/html/dist_keycloak;
            index index.html index.htm;
            try_files \$uri \$uri/ /index.html;
            
            # 添加MIME类型支持
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }
        
        # API代理
        location /api/ {
            proxy_pass http://*************:9084/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
        }
        
        # 健康检查
        location /health {
            add_header Content-Type application/json;
            return 200 '{"status": "ok", "service": "nginx", "timestamp": "\$time_iso8601"}';
        }
    }
}
EOF

echo "✅ nginx配置已更新"

# 5. 测试配置并重启
echo ""
echo "步骤4: 测试并重启nginx..."

if sudo nginx -t; then
    echo "✅ nginx配置测试通过"
    sudo nginx -s reload 2>/dev/null || sudo nginx
    echo "✅ nginx已重新加载"
else
    echo "❌ nginx配置测试失败"
    exit 1
fi

# 6. 清理临时文件
if [ -d "temp_frontend" ]; then
    rm -rf temp_frontend
fi

# 7. 测试访问
echo ""
echo "步骤5: 测试访问..."
sleep 2

echo "测试本地HTTPS访问:"
if curl -k -f https://localhost:8089 > /dev/null 2>&1; then
    echo "✅ HTTPS访问正常"
else
    echo "❌ HTTPS访问失败"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "✅ 404错误应该已经解决"
echo ""
echo "访问地址:"
echo "  HTTPS: https://*************:9082"
echo "  HTTP: http://*************:9083 (重定向到HTTPS)"
echo ""
echo "前端文件位置: $FRONTEND_TARGET"
echo "nginx配置文件: /usr/local/nginx/conf/nginx.conf"
echo ""
echo "如果仍有问题，请查看nginx日志:"
echo "  sudo tail -f /var/log/nginx/error.log"
