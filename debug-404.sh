#!/bin/bash

# 调试404错误的详细脚本

echo "=== 调试404错误 ==="

# 1. 检查nginx进程和配置
echo "1. nginx基本信息:"
echo "nginx进程:"
ps aux | grep nginx | grep -v grep

echo ""
echo "nginx配置测试:"
sudo nginx -t

echo ""
echo "nginx版本和编译信息:"
nginx -V

# 2. 检查nginx工作目录
echo ""
echo "2. nginx工作目录:"
NGINX_PREFIX=$(nginx -V 2>&1 | grep -o 'prefix=[^ ]*' | cut -d= -f2)
echo "nginx prefix: $NGINX_PREFIX"

# 3. 检查可能的前端文件位置
echo ""
echo "3. 检查前端文件位置:"

POSSIBLE_PATHS=(
    "$NGINX_PREFIX/html/dist_keycloak"
    "/usr/local/nginx/html/dist_keycloak"
    "/var/www/html/dist_keycloak"
    "/usr/share/nginx/html/dist_keycloak"
    "html/dist_keycloak"
    "./html/dist_keycloak"
)

for path in "${POSSIBLE_PATHS[@]}"; do
    if [ -d "$path" ]; then
        echo "✅ 找到目录: $path"
        if [ -f "$path/index.html" ]; then
            echo "  ✅ 包含index.html"
            echo "  文件数量: $(find "$path" -type f | wc -l)"
            echo "  目录大小: $(du -sh "$path" 2>/dev/null | cut -f1)"
        else
            echo "  ❌ 缺少index.html"
        fi
    else
        echo "❌ 目录不存在: $path"
    fi
done

# 4. 检查当前目录的dist文件
echo ""
echo "4. 检查当前目录的前端文件:"
if [ -d "web/dist" ]; then
    echo "✅ 找到web/dist目录"
    echo "  文件数量: $(find web/dist -type f | wc -l)"
    if [ -f "web/dist/index.html" ]; then
        echo "  ✅ 包含index.html"
    else
        echo "  ❌ 缺少index.html"
    fi
fi

# 5. 检查nginx错误日志
echo ""
echo "5. nginx错误日志 (最近10行):"
if [ -f "/var/log/nginx/error.log" ]; then
    echo "从 /var/log/nginx/error.log:"
    sudo tail -10 /var/log/nginx/error.log
elif [ -f "$NGINX_PREFIX/logs/error.log" ]; then
    echo "从 $NGINX_PREFIX/logs/error.log:"
    sudo tail -10 "$NGINX_PREFIX/logs/error.log"
else
    echo "未找到错误日志文件"
fi

# 6. 检查nginx访问日志
echo ""
echo "6. nginx访问日志 (最近5行):"
if [ -f "/var/log/nginx/access.log" ]; then
    echo "从 /var/log/nginx/access.log:"
    sudo tail -5 /var/log/nginx/access.log
elif [ -f "$NGINX_PREFIX/logs/access.log" ]; then
    echo "从 $NGINX_PREFIX/logs/access.log:"
    sudo tail -5 "$NGINX_PREFIX/logs/access.log"
else
    echo "未找到访问日志文件"
fi

# 7. 测试文件访问权限
echo ""
echo "7. 测试文件访问权限:"
TEST_FILE="$NGINX_PREFIX/html/dist_keycloak/index.html"
if [ -f "$TEST_FILE" ]; then
    echo "测试文件: $TEST_FILE"
    echo "文件权限: $(ls -la "$TEST_FILE")"
    echo "目录权限: $(ls -lad "$(dirname "$TEST_FILE")")"
    
    # 测试nginx用户是否能读取
    NGINX_USER=$(ps aux | grep nginx | grep -v grep | head -1 | awk '{print $1}')
    echo "nginx运行用户: $NGINX_USER"
    
    if [ "$NGINX_USER" != "root" ]; then
        echo "测试用户权限:"
        sudo -u "$NGINX_USER" test -r "$TEST_FILE" && echo "✅ nginx用户可以读取文件" || echo "❌ nginx用户无法读取文件"
    fi
fi

# 8. 显示完整的nginx配置
echo ""
echo "8. 当前nginx配置中的location /:"
sudo nginx -T | grep -A 10 "location /"

echo ""
echo "=== 调试完成 ==="
echo ""
echo "建议的修复步骤:"
echo "1. 确认前端文件位置"
echo "2. 修正nginx配置中的root路径"
echo "3. 设置正确的文件权限"
echo "4. 重新加载nginx配置"
