# 端口配置说明

## 容器端口映射

根据您的配置，端口映射如下：

| 服务 | 容器外端口 | 容器内端口 | 协议 | 说明 |
|------|------------|------------|------|------|
| 前端应用 (HTTPS) | 9082 | 8089 | HTTPS | 主要应用入口 |
| 前端应用 (HTTP) | 9083 | 8088 | HTTP | 重定向到HTTPS |
| 后端API | 9084 | 8099 | HTTP | API代理服务 |
| Keycloak (HTTP) | 9087 | 8080 | HTTP | 认证服务 |
| Keycloak (HTTPS) | 9088 | 8090 | HTTPS | 认证服务 |

## 访问地址

### 前端应用
- **主要入口 (HTTPS)**: https://111.13.109.67:9082
- **HTTP入口**: http://111.13.109.67:9083 (自动重定向到HTTPS)

### 后端API
- **通过nginx代理**: https://111.13.109.67:9082/api/
- **直接访问**: http://111.13.109.67:9084/
- **健康检查**: http://111.13.109.67:9084/health

### Keycloak认证服务
- **HTTPS**: https://111.13.109.67:9088
- **HTTP**: http://111.13.109.67:9087

## nginx配置要点

### 前端服务器配置
```nginx
# HTTP重定向到HTTPS
server {
    listen 8088;
    return 301 https://$server_name:9082$request_uri;
}

# HTTPS主服务器
server {
    listen 8089 ssl http2;
    # SSL证书配置
    ssl_certificate /usr/local/nginx/ssl/server.crt;
    ssl_certificate_key /usr/local/nginx/ssl/server.key;
}
```

### API代理配置
```nginx
location /api/ {
    proxy_pass http://111.13.109.67:9084/;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Forwarded-Port 9082;
}
```

### WebSocket支持
```nginx
location /socket.io/ {
    proxy_pass http://111.13.109.67:9084;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
}
```

## 防火墙配置

确保以下端口在防火墙中开放：

```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=9082/tcp  # HTTPS前端
sudo firewall-cmd --permanent --add-port=9083/tcp  # HTTP前端
sudo firewall-cmd --permanent --add-port=9084/tcp  # API服务
sudo firewall-cmd --permanent --add-port=9087/tcp  # Keycloak HTTP
sudo firewall-cmd --permanent --add-port=9088/tcp  # Keycloak HTTPS
sudo firewall-cmd --reload
```

## SSL证书配置

SSL证书位置：
- 证书文件: `/usr/local/nginx/ssl/server.crt`
- 私钥文件: `/usr/local/nginx/ssl/server.key`

生成SSL证书：
```bash
chmod +x generate-ssl-cert.sh
./generate-ssl-cert.sh
```

## 部署命令

```bash
# 快速部署
chmod +x deploy.sh
./deploy.sh

# 或使用Docker Compose
docker-compose up -d --build
```

## 验证部署

1. **检查前端**: https://111.13.109.67:9082
2. **检查API**: https://111.13.109.67:9082/api/health
3. **检查后端**: http://111.13.109.67:9084/health
4. **检查Keycloak**: https://111.13.109.67:9088

## 故障排除

### 常见问题

1. **SSL证书错误**
   ```bash
   # 重新生成证书
   ./generate-ssl-cert.sh
   ```

2. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :9082
   ```

3. **nginx配置错误**
   ```bash
   # 测试配置
   nginx -t
   ```

4. **容器无法启动**
   ```bash
   # 查看日志
   docker-compose logs -f frontend
   docker-compose logs -f backend
   ```
