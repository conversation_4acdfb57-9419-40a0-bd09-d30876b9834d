"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8017],{44859:function(y,n,s){s.r(n),s.d(n,{default:function(){return g}});var u=s(5574),l=s.n(u),r=s(84226),i=s(83622),o=s(29905),S=s(67294),c=s(28846),d=(0,c.kc)(function(){return{registerResult:{width:"800px",minHeight:"400px",margin:"auto",padding:"80px",background:"none"},anticon:{fontSize:"64px"},title:{marginTop:"32px",fontSize:"20px",lineHeight:"28px"},actions:{marginTop:"40px","a + a":{marginLeft:"8px"}}}}),x=d,t=s(85893),v=function(){var m=x(),a=m.styles,h=(0,r.useSearchParams)(),p=l()(h,1),e=p[0],f=(0,t.jsxs)("div",{className:a.actions,children:[(0,t.jsx)("a",{href:"",children:(0,t.jsx)(i.ZP,{size:"large",type:"primary",children:(0,t.jsx)("span",{children:"\u67E5\u770B\u90AE\u7BB1"})})}),(0,t.jsx)(r.Link,{to:"/",children:(0,t.jsx)(i.ZP,{size:"large",children:"\u8FD4\u56DE\u9996\u9875"})})]}),j=(e==null?void 0:e.get("account"))||"<EMAIL>";return(0,t.jsx)(o.ZP,{className:a.registerResult,status:"success",title:(0,t.jsx)("div",{className:a.title,children:(0,t.jsxs)("span",{children:["\u4F60\u7684\u8D26\u6237\uFF1A",j," \u6CE8\u518C\u6210\u529F"]})}),extra:f})},g=v}}]);
