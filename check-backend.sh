#!/bin/bash

# 检查后端服务状态

echo "=== 后端服务检查 ==="

# 1. 检查端口9084
echo "1. 检查端口9084状态:"
if netstat -tlnp | grep :9084; then
    echo "✅ 端口9084正在监听"
else
    echo "❌ 端口9084未监听"
fi

# 2. 检查后端健康状态
echo ""
echo "2. 测试后端连接:"
if curl -f http://111.13.109.67:9084/health 2>/dev/null; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务不可用"
    echo "尝试启动后端服务..."
    
    # 检查是否有Docker容器
    if command -v docker &> /dev/null; then
        echo "检查Docker容器:"
        docker ps | grep backend || echo "未找到backend容器"
        
        echo "尝试启动Docker容器:"
        docker-compose up -d backend 2>/dev/null || echo "Docker Compose启动失败"
    fi
    
    # 检查Node.js进程
    if command -v node &> /dev/null; then
        echo "检查Node.js后端进程:"
        ps aux | grep node | grep -v grep || echo "未找到Node.js进程"
        
        if [ -d "server" ]; then
            echo "尝试启动Node.js后端:"
            cd server
            if [ -f "package.json" ]; then
                echo "安装依赖..."
                npm install
                echo "启动后端服务..."
                nohup npm start > ../backend.log 2>&1 &
                echo "后端服务已在后台启动，日志文件: backend.log"
            fi
            cd ..
        fi
    fi
fi

echo ""
echo "3. 端口占用情况:"
netstat -tlnp | grep -E ":(8088|8089|9082|9083|9084|9087|9088)"

echo ""
echo "=== 检查完成 ==="
