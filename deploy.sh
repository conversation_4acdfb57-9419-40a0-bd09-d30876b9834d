#!/bin/bash

# 部署脚本 - 配置HTTPS环境解决Web Crypto API问题

set -e

echo "=== 开始部署Web应用 ==="

# 1. 生成SSL证书
echo "步骤1: 生成SSL证书..."
chmod +x generate-ssl-cert.sh
./generate-ssl-cert.sh

# 2. 创建SSL目录（如果不存在）
echo "步骤2: 准备SSL目录..."
mkdir -p ssl
sudo cp /etc/nginx/ssl/server.crt ssl/
sudo cp /etc/nginx/ssl/server.key ssl/
sudo chown $USER:$USER ssl/server.*

# 3. 构建前端应用
echo "步骤3: 构建前端应用..."
cd web
npm install
npm run build
cd ..

# 4. 构建后端应用
echo "步骤4: 准备后端应用..."
cd server
npm install
cd ..

# 5. 停止现有容器
echo "步骤5: 停止现有容器..."
docker-compose down || true

# 6. 构建并启动容器
echo "步骤6: 启动应用容器..."
docker-compose up -d --build

# 7. 等待服务启动
echo "步骤7: 等待服务启动..."
sleep 10

# 8. 检查服务状态
echo "步骤8: 检查服务状态..."
docker-compose ps

# 9. 显示访问信息
echo ""
echo "=== 部署完成 ==="
echo "应用访问地址:"
echo "  HTTPS: https://111.13.109.67:8089"
echo "  HTTP (重定向): http://111.13.109.67:8088"
echo ""
echo "后端API地址:"
echo "  健康检查: https://111.13.109.67:8089/api/health"
echo ""
echo "注意事项:"
echo "1. 首次访问时浏览器会显示安全警告，请点击'高级'并选择'继续访问'"
echo "2. 确保Keycloak服务器正在运行并可访问"
echo "3. 检查防火墙是否开放了8088和8089端口"
echo ""
echo "查看日志:"
echo "  docker-compose logs -f frontend"
echo "  docker-compose logs -f backend"
