#!/bin/bash

# 快速部署脚本 - HTTP模式，解决SSL证书问题

set -e

echo "=== 快速部署Web应用 (HTTP模式) ==="

# 1. 检查nginx是否支持SSL
echo "步骤1: 检查nginx配置..."
if nginx -V 2>&1 | grep -q "http_ssl_module"; then
    echo "✅ nginx支持SSL模块"
    
    # 询问用户是否要生成SSL证书
    read -p "是否生成SSL证书并使用HTTPS? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "生成SSL证书..."
        chmod +x generate-ssl-cert.sh
        ./generate-ssl-cert.sh
        
        echo "使用HTTPS配置..."
        CONFIG_FILE="nginx.conf"
    else
        echo "使用HTTP配置..."
        CONFIG_FILE="nginx-http.conf"
    fi
else
    echo "⚠️  nginx不支持SSL模块，使用HTTP配置"
    CONFIG_FILE="nginx-http.conf"
fi

# 2. 备份当前nginx配置
echo "步骤2: 备份nginx配置..."
if [ -f "/usr/local/nginx/conf/nginx.conf" ]; then
    sudo cp /usr/local/nginx/conf/nginx.conf /usr/local/nginx/conf/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份原配置文件"
fi

# 3. 复制新配置
echo "步骤3: 应用新配置..."
sudo cp $CONFIG_FILE /usr/local/nginx/conf/nginx.conf

# 4. 测试nginx配置
echo "步骤4: 测试nginx配置..."
if sudo nginx -t; then
    echo "✅ nginx配置测试通过"
else
    echo "❌ nginx配置测试失败，恢复备份配置"
    if [ -f "/usr/local/nginx/conf/nginx.conf.backup.*" ]; then
        sudo cp /usr/local/nginx/conf/nginx.conf.backup.* /usr/local/nginx/conf/nginx.conf
    fi
    exit 1
fi

# 5. 构建前端应用
echo "步骤5: 构建前端应用..."
cd web
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
fi

echo "构建前端应用..."
npm run build

# 6. 部署前端文件
echo "步骤6: 部署前端文件..."
if [ -d "dist" ]; then
    sudo rm -rf /usr/local/nginx/html/dist_keycloak
    sudo cp -r dist /usr/local/nginx/html/dist_keycloak
    echo "✅ 前端文件部署完成"
else
    echo "❌ 前端构建失败，未找到dist目录"
    exit 1
fi

cd ..

# 7. 启动/重启nginx
echo "步骤7: 重启nginx..."
if pgrep nginx > /dev/null; then
    sudo nginx -s reload
    echo "✅ nginx已重新加载配置"
else
    sudo nginx
    echo "✅ nginx已启动"
fi

# 8. 检查服务状态
echo "步骤8: 检查服务状态..."
sleep 2

if curl -f http://111.13.109.67:8088 > /dev/null 2>&1; then
    echo "✅ 前端服务正常"
else
    echo "⚠️  前端服务可能未正常启动"
fi

# 9. 显示访问信息
echo ""
echo "=== 部署完成 ==="
echo "应用访问地址:"
if [ "$CONFIG_FILE" = "nginx.conf" ]; then
    echo "  HTTPS: https://111.13.109.67:8089"
    echo "  HTTP (重定向): http://111.13.109.67:8088"
else
    echo "  HTTP: http://111.13.109.67:8088"
fi
echo ""
echo "注意事项:"
echo "1. 应用已配置Web Crypto API polyfill以支持HTTP环境"
echo "2. 确保后端服务正在运行 (端口9084)"
echo "3. 确保Keycloak服务器正在运行并可访问"
echo "4. 检查防火墙是否开放了8088端口"
echo ""
echo "查看nginx日志:"
echo "  sudo tail -f /var/log/nginx/access.log"
echo "  sudo tail -f /var/log/nginx/error.log"
