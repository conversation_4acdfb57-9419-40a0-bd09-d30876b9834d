/**
 * Keycloak环境诊断工具
 * 用于检测和诊断Keycloak相关的环境问题
 */

export interface DiagnosticResult {
  name: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  suggestion?: string;
}

export class KeycloakDiagnostics {
  
  /**
   * 检查Web Crypto API支持
   */
  static checkWebCryptoAPI(): DiagnosticResult {
    const isSupported = typeof window !== 'undefined' && 
                       window.crypto && 
                       window.crypto.subtle;
    
    if (isSupported) {
      return {
        name: 'Web Crypto API',
        status: 'success',
        message: 'Web Crypto API 可用'
      };
    } else {
      return {
        name: 'Web Crypto API',
        status: 'error',
        message: 'Web Crypto API 不可用',
        suggestion: '请使用支持Web Crypto API的现代浏览器，或确保在安全上下文中运行'
      };
    }
  }

  /**
   * 检查安全上下文
   */
  static checkSecureContext(): DiagnosticResult {
    const isSecure = typeof window !== 'undefined' && 
                    (window.location.protocol === 'https:' || 
                     window.location.hostname === 'localhost' || 
                     window.location.hostname === '127.0.0.1' ||
                     window.isSecureContext);
    
    if (isSecure) {
      return {
        name: '安全上下文',
        status: 'success',
        message: '当前运行在安全上下文中'
      };
    } else {
      return {
        name: '安全上下文',
        status: 'warning',
        message: '当前未运行在安全上下文中',
        suggestion: '建议使用HTTPS协议或在localhost环境下测试'
      };
    }
  }

  /**
   * 检查浏览器兼容性
   */
  static checkBrowserCompatibility(): DiagnosticResult {
    const userAgent = navigator.userAgent;
    const isModernBrowser = 'fetch' in window && 
                           'Promise' in window && 
                           'URLSearchParams' in window;
    
    if (isModernBrowser) {
      return {
        name: '浏览器兼容性',
        status: 'success',
        message: '浏览器支持现代Web API'
      };
    } else {
      return {
        name: '浏览器兼容性',
        status: 'error',
        message: '浏览器版本过旧',
        suggestion: '请升级到支持ES6+的现代浏览器'
      };
    }
  }

  /**
   * 检查网络连接
   */
  static async checkKeycloakConnection(keycloakUrl: string): Promise<DiagnosticResult> {
    try {
      const response = await fetch(`${keycloakUrl}/realms/dev_xh_key/.well-known/openid_configuration`, {
        method: 'GET',
        mode: 'cors'
      });
      
      if (response.ok) {
        return {
          name: 'Keycloak连接',
          status: 'success',
          message: 'Keycloak服务器连接正常'
        };
      } else {
        return {
          name: 'Keycloak连接',
          status: 'error',
          message: `Keycloak服务器响应错误: ${response.status}`,
          suggestion: '请检查Keycloak服务器是否正常运行'
        };
      }
    } catch (error) {
      return {
        name: 'Keycloak连接',
        status: 'error',
        message: `无法连接到Keycloak服务器: ${error}`,
        suggestion: '请检查网络连接和Keycloak服务器地址'
      };
    }
  }

  /**
   * 检查CORS配置
   */
  static checkCORSConfiguration(): DiagnosticResult {
    const origin = window.location.origin;
    
    return {
      name: 'CORS配置',
      status: 'warning',
      message: `当前域名: ${origin}`,
      suggestion: '请确保Keycloak客户端配置中包含此域名的重定向URI'
    };
  }

  /**
   * 运行完整诊断
   */
  // static async runFullDiagnostics(keycloakUrl: string = 'http://localhost:8080'): Promise<DiagnosticResult[]> {
    static async runFullDiagnostics(keycloakUrl: string = 'http://localhost:8080'): Promise<DiagnosticResult[]> {  
  const results: DiagnosticResult[] = [];
    
    // 同步检查
    results.push(this.checkWebCryptoAPI());
    results.push(this.checkSecureContext());
    results.push(this.checkBrowserCompatibility());
    results.push(this.checkCORSConfiguration());
    
    // 异步检查
    try {
      const connectionResult = await this.checkKeycloakConnection(keycloakUrl);
      results.push(connectionResult);
    } catch (error) {
      results.push({
        name: 'Keycloak连接',
        status: 'error',
        message: '诊断过程中发生错误',
        suggestion: '请检查网络连接'
      });
    }
    
    return results;
  }

  /**
   * 生成诊断报告
   */
  static generateReport(results: DiagnosticResult[]): string {
    let report = '=== Keycloak环境诊断报告 ===\n\n';
    
    results.forEach((result, index) => {
      const statusIcon = result.status === 'success' ? '✅' : 
                        result.status === 'warning' ? '⚠️' : '❌';
      
      report += `${index + 1}. ${statusIcon} ${result.name}\n`;
      report += `   状态: ${result.message}\n`;
      
      if (result.suggestion) {
        report += `   建议: ${result.suggestion}\n`;
      }
      
      report += '\n';
    });
    
    // 添加总结
    const errorCount = results.filter(r => r.status === 'error').length;
    const warningCount = results.filter(r => r.status === 'warning').length;
    
    report += '=== 总结 ===\n';
    if (errorCount === 0 && warningCount === 0) {
      report += '✅ 所有检查都通过，环境配置正常\n';
    } else {
      report += `❌ 发现 ${errorCount} 个错误，${warningCount} 个警告\n`;
      report += '请根据上述建议进行修复\n';
    }
    
    return report;
  }

  /**
   * 在控制台输出诊断报告
   */
  static async logDiagnostics(keycloakUrl?: string): Promise<void> {
    console.log('🔍 开始Keycloak环境诊断...');
    
    const results = await this.runFullDiagnostics(keycloakUrl);
    const report = this.generateReport(results);
    
    console.log(report);
  }
}

// 导出便捷函数
export const runKeycloakDiagnostics = KeycloakDiagnostics.logDiagnostics;
