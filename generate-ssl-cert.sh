#!/bin/bash

# SSL证书生成脚本
# 用于为nginx生成自签名SSL证书

set -e

# 配置变量
DOMAIN="*************"
SSL_DIR="/usr/local/nginx/ssl"
CERT_FILE="$SSL_DIR/server.crt"
KEY_FILE="$SSL_DIR/server.key"
CSR_FILE="$SSL_DIR/server.csr"

echo "=== 开始生成SSL证书 ==="

# 创建SSL目录
echo "创建SSL证书目录..."
 mkdir -p $SSL_DIR

# 生成私钥
echo "生成私钥..."
 openssl genrsa -out $KEY_FILE 2048

# 生成证书签名请求
echo "生成证书签名请求..."
 openssl req -new -key $KEY_FILE -out $CSR_FILE -subj "/C=CN/ST=Beijing/L=Beijing/O=Company/OU=IT/CN=$DOMAIN"

# 创建扩展配置文件
echo "创建证书扩展配置..."
cat > /tmp/cert_extensions.conf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C=CN
ST=Beijing
L=Beijing
O=Company
OU=IT
CN=$DOMAIN

[v3_req]
basicConstraints = CA:FALSE
keyUsage = critical, digitalSignature, keyEncipherment, keyAgreement
extendedKeyUsage = critical, serverAuth, clientAuth
subjectAltName = @alt_names
subjectKeyIdentifier = hash

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = localhost
IP.1 = $DOMAIN
IP.2 = 127.0.0.1
EOF

# 生成自签名证书（包含SAN扩展）
echo "生成自签名证书..."
 openssl x509 -req -days 365 -in $CSR_FILE -signkey $KEY_FILE -out $CERT_FILE -extensions v3_req -extfile /tmp/cert_extensions.conf

# 清理临时文件
rm -f /tmp/cert_extensions.conf

# 设置权限
echo "设置证书权限..."
 chmod 600 $KEY_FILE
 chmod 644 $CERT_FILE

# 验证证书
echo "验证证书..."
 openssl x509 -in $CERT_FILE -text -noout | grep -E "(Subject:|Issuer:|Not Before:|Not After:)"
echo ""
echo "检查SAN扩展:"
 openssl x509 -in $CERT_FILE -text -noout | grep -A 1 "Subject Alternative Name"

echo ""
echo "=== SSL证书生成完成 ==="
echo "证书文件: $CERT_FILE"
echo "私钥文件: $KEY_FILE"
echo ""
echo "请重启nginx服务以应用新证书:"
echo " systemctl restart nginx"
echo ""
echo "访问地址: https://*************:8089"
echo "注意: 由于使用自签名证书，浏览器会显示安全警告，请点击'高级'并选择'继续访问'"
