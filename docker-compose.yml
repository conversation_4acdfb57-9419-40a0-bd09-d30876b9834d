version: '3.8'

services:
  # 前端应用
  frontend:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: web_app_frontend
    ports:
      - "9083:8088"  # HTTP端口 (容器外:容器内)
      - "9082:8089"  # HTTPS端口 (容器外:容器内)
    volumes:
      - ./nginx.conf:/usr/local/nginx/conf/nginx.conf:ro
      - ./ssl:/usr/local/nginx/ssl:ro
      - ./web/dist:/usr/local/nginx/html/dist_keycloak:ro
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - app-network

  # 后端应用
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: web_app_backend
    ports:
      - "9084:8099"  # API端口 (容器外:容器内)
    environment:
      - NODE_ENV=production
      - KEYCLOAK_URL=https://111.13.109.67:9088
      - KEYCLOAK_REALM=dev_xh_key
      - KEYCLOAK_CLIENT_ID=sulei_01
      - KEYCLOAK_CLIENT_SECRET=${KEYCLOAK_CLIENT_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
      - PORT=8099
    volumes:
      - ./server/src:/app/src:ro
    restart: unless-stopped
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  ssl-certs:
    driver: local
