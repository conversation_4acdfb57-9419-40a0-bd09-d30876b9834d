version: '3.8'

services:
  # 前端应用
  frontend:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: web_app_frontend
    ports:
      - "8088:8088"  # HTTP端口
      - "8089:8089"  # HTTPS端口
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./web/dist:/usr/share/nginx/html:ro
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - app-network

  # 后端应用
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: web_app_backend
    ports:
      - "8001:8001"
    environment:
      - NODE_ENV=production
      - KEYCLOAK_URL=https://111.13.109.67:9088
      - KEYCLOAK_REALM=dev_xh_key
      - KEYCLOAK_CLIENT_ID=sulei_01
      - KEYCLOAK_CLIENT_SECRET=${KEYCLOAK_CLIENT_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
    volumes:
      - ./server/src:/app/src:ro
    restart: unless-stopped
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  ssl-certs:
    driver: local
