"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[411],{40411:function(Vt,dt,y){y.d(dt,{Z:function(){return Pt}});var r=y(67294),ut=y(93967),w=y.n(ut),mt=y(29372),J=y(98787),K=y(96159),F=y(53124),h=y(11568),Q=y(14747),q=y(98719),bt=y(83262),k=y(83559);const gt=new h.E4("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),ft=new h.E4("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),pt=new h.E4("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),vt=new h.E4("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),Ct=new h.E4("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),ht=new h.E4("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),$t=t=>{const{componentCls:e,iconCls:i,antCls:n,badgeShadowSize:o,textFontSize:d,textFontSizeSM:a,statusSize:$,dotSize:v,textFontWeight:C,indicatorHeight:l,indicatorHeightSM:c,marginXS:m,calc:g}=t,u=`${n}-scroll-number`,S=(0,q.Z)(t,(f,{darkColor:N})=>({[`&${e} ${e}-color-${f}`]:{background:N,[`&:not(${e}-count)`]:{color:N},"a:hover &":{background:N}}}));return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,Q.Wf)(t)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${e}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:t.indicatorZIndex,minWidth:l,height:l,color:t.badgeTextColor,fontWeight:C,fontSize:d,lineHeight:(0,h.bf)(l),whiteSpace:"nowrap",textAlign:"center",background:t.badgeColor,borderRadius:g(l).div(2).equal(),boxShadow:`0 0 0 ${(0,h.bf)(o)} ${t.badgeShadowColor}`,transition:`background ${t.motionDurationMid}`,a:{color:t.badgeTextColor},"a:hover":{color:t.badgeTextColor},"a:hover &":{background:t.badgeColorHover}},[`${e}-count-sm`]:{minWidth:c,height:c,fontSize:a,lineHeight:(0,h.bf)(c),borderRadius:g(c).div(2).equal()},[`${e}-multiple-words`]:{padding:`0 ${(0,h.bf)(t.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${e}-dot`]:{zIndex:t.indicatorZIndex,width:v,minWidth:v,height:v,background:t.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${(0,h.bf)(o)} ${t.badgeShadowColor}`},[`${e}-count, ${e}-dot, ${u}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${i}-spin`]:{animationName:ht,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${e}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${e}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:$,height:$,verticalAlign:"middle",borderRadius:"50%"},[`${e}-status-success`]:{backgroundColor:t.colorSuccess},[`${e}-status-processing`]:{overflow:"visible",color:t.colorInfo,backgroundColor:t.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:o,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:gt,animationDuration:t.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${e}-status-default`]:{backgroundColor:t.colorTextPlaceholder},[`${e}-status-error`]:{backgroundColor:t.colorError},[`${e}-status-warning`]:{backgroundColor:t.colorWarning},[`${e}-status-text`]:{marginInlineStart:m,color:t.colorText,fontSize:t.fontSize}}}),S),{[`${e}-zoom-appear, ${e}-zoom-enter`]:{animationName:ft,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`${e}-zoom-leave`]:{animationName:pt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`&${e}-not-a-wrapper`]:{[`${e}-zoom-appear, ${e}-zoom-enter`]:{animationName:vt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`${e}-zoom-leave`]:{animationName:Ct,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`&:not(${e}-status)`]:{verticalAlign:"middle"},[`${u}-custom-component, ${e}-count`]:{transform:"none"},[`${u}-custom-component, ${u}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[u]:{overflow:"hidden",transition:`all ${t.motionDurationMid} ${t.motionEaseOutBack}`,[`${u}-only`]:{position:"relative",display:"inline-block",height:l,transition:`all ${t.motionDurationSlow} ${t.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${u}-only-unit`]:{height:l,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${u}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${e}-count, ${e}-dot, ${u}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}},_=t=>{const{fontHeight:e,lineWidth:i,marginXS:n,colorBorderBg:o}=t,d=e,a=i,$=t.colorTextLightSolid,v=t.colorError,C=t.colorErrorHover;return(0,bt.IX)(t,{badgeFontHeight:d,badgeShadowSize:a,badgeTextColor:$,badgeColor:v,badgeColorHover:C,badgeShadowColor:o,badgeProcessingDuration:"1.2s",badgeRibbonOffset:n,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},tt=t=>{const{fontSize:e,lineHeight:i,fontSizeSM:n,lineWidth:o}=t;return{indicatorZIndex:"auto",indicatorHeight:Math.round(e*i)-2*o,indicatorHeightSM:e,dotSize:n/2,textFontSize:n,textFontSizeSM:n,textFontWeight:"normal",statusSize:n/2}};var yt=(0,k.I$)("Badge",t=>{const e=_(t);return $t(e)},tt);const St=t=>{const{antCls:e,badgeFontHeight:i,marginXS:n,badgeRibbonOffset:o,calc:d}=t,a=`${e}-ribbon`,$=`${e}-ribbon-wrapper`,v=(0,q.Z)(t,(C,{darkColor:l})=>({[`&${a}-color-${C}`]:{background:l,color:l}}));return{[$]:{position:"relative"},[a]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,Q.Wf)(t)),{position:"absolute",top:n,padding:`0 ${(0,h.bf)(t.paddingXS)}`,color:t.colorPrimary,lineHeight:(0,h.bf)(i),whiteSpace:"nowrap",backgroundColor:t.colorPrimary,borderRadius:t.borderRadiusSM,[`${a}-text`]:{color:t.badgeTextColor},[`${a}-corner`]:{position:"absolute",top:"100%",width:o,height:o,color:"currentcolor",border:`${(0,h.bf)(d(o).div(2).equal())} solid`,transform:t.badgeRibbonCornerTransform,transformOrigin:"top",filter:t.badgeRibbonCornerFilter}}),v),{[`&${a}-placement-end`]:{insetInlineEnd:d(o).mul(-1).equal(),borderEndEndRadius:0,[`${a}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${a}-placement-start`]:{insetInlineStart:d(o).mul(-1).equal(),borderEndStartRadius:0,[`${a}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}};var Nt=(0,k.I$)(["Badge","Ribbon"],t=>{const e=_(t);return St(e)},tt),Ot=t=>{const{className:e,prefixCls:i,style:n,color:o,children:d,text:a,placement:$="end",rootClassName:v}=t,{getPrefixCls:C,direction:l}=r.useContext(F.E_),c=C("ribbon",i),m=`${c}-wrapper`,[g,u,S]=Nt(c,m),f=(0,J.o2)(o,!1),N=w()(c,`${c}-placement-${$}`,{[`${c}-rtl`]:l==="rtl",[`${c}-color-${o}`]:f},e),b={},x={};return o&&!f&&(b.background=o,x.color=o),g(r.createElement("div",{className:w()(m,v,u,S)},d,r.createElement("div",{className:w()(N,u),style:Object.assign(Object.assign({},b),n)},r.createElement("span",{className:`${c}-text`},a),r.createElement("div",{className:`${c}-corner`,style:x}))))};const et=t=>{const{prefixCls:e,value:i,current:n,offset:o=0}=t;let d;return o&&(d={position:"absolute",top:`${o}00%`,left:0}),r.createElement("span",{style:d,className:w()(`${e}-only-unit`,{current:n})},i)};function xt(t,e,i){let n=t,o=0;for(;(n+10)%10!==e;)n+=i,o+=i;return o}var Et=t=>{const{prefixCls:e,count:i,value:n}=t,o=Number(n),d=Math.abs(i),[a,$]=r.useState(o),[v,C]=r.useState(d),l=()=>{$(o),C(d)};r.useEffect(()=>{const g=setTimeout(l,1e3);return()=>clearTimeout(g)},[o]);let c,m;if(a===o||Number.isNaN(o)||Number.isNaN(a))c=[r.createElement(et,Object.assign({},t,{key:o,current:!0}))],m={transition:"none"};else{c=[];const g=o+10,u=[];for(let b=o;b<=g;b+=1)u.push(b);const S=v<d?1:-1,f=u.findIndex(b=>b%10===a);c=(S<0?u.slice(0,f+1):u.slice(f)).map((b,x)=>{const Z=b%10;return r.createElement(et,Object.assign({},t,{key:b,value:Z,offset:S<0?x-f:x,current:x===f}))}),m={transform:`translateY(${-xt(a,o,S)}00%)`}}return r.createElement("span",{className:`${e}-only`,style:m,onTransitionEnd:l},c)},wt=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(i[n[o]]=t[n[o]]);return i},jt=r.forwardRef((t,e)=>{const{prefixCls:i,count:n,className:o,motionClassName:d,style:a,title:$,show:v,component:C="sup",children:l}=t,c=wt(t,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:m}=r.useContext(F.E_),g=m("scroll-number",i),u=Object.assign(Object.assign({},c),{"data-show":v,style:a,className:w()(g,o,d),title:$});let S=n;if(n&&Number(n)%1===0){const f=String(n).split("");S=r.createElement("bdi",null,f.map((N,b)=>r.createElement(Et,{prefixCls:g,count:Number(n),value:N,key:f.length-b})))}return a!=null&&a.borderColor&&(u.style=Object.assign(Object.assign({},a),{boxShadow:`0 0 0 1px ${a.borderColor} inset`})),l?(0,K.Tm)(l,f=>({className:w()(`${g}-custom-component`,f==null?void 0:f.className,d)})):r.createElement(C,Object.assign({},u,{ref:e}),S)}),It=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(i[n[o]]=t[n[o]]);return i};const ot=r.forwardRef((t,e)=>{var i,n,o,d,a;const{prefixCls:$,scrollNumberPrefixCls:v,children:C,status:l,text:c,color:m,count:g=null,overflowCount:u=99,dot:S=!1,size:f="default",title:N,offset:b,style:x,className:Z,rootClassName:Tt,classNames:j,styles:O,showZero:H=!1}=t,nt=It(t,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:st,direction:M,badge:s}=r.useContext(F.E_),p=st("badge",$),[rt,zt,Rt]=yt(p),V=g>u?`${u}+`:g,R=V==="0"||V===0,Bt=g===null||R&&!H,A=(l!=null||m!=null)&&Bt,z=S&&!R,I=z?"":V,P=(0,r.useMemo)(()=>(I==null||I===""||R&&!H)&&!z,[I,R,H,z]),it=(0,r.useRef)(g);P||(it.current=g);const T=it.current,at=(0,r.useRef)(I);P||(at.current=I);const L=at.current,lt=(0,r.useRef)(z);P||(lt.current=z);const B=(0,r.useMemo)(()=>{if(!b)return Object.assign(Object.assign({},s==null?void 0:s.style),x);const E={marginTop:b[1]};return M==="rtl"?E.left=parseInt(b[0],10):E.right=-parseInt(b[0],10),Object.assign(Object.assign(Object.assign({},E),s==null?void 0:s.style),x)},[M,b,x,s==null?void 0:s.style]),Wt=N!=null?N:typeof T=="string"||typeof T=="number"?T:void 0,Dt=P||!c?null:r.createElement("span",{className:`${p}-status-text`},c),Ft=!T||typeof T!="object"?void 0:(0,K.Tm)(T,E=>({style:Object.assign(Object.assign({},B),E.style)})),W=(0,J.o2)(m,!1),Zt=w()(j==null?void 0:j.indicator,(i=s==null?void 0:s.classNames)===null||i===void 0?void 0:i.indicator,{[`${p}-status-dot`]:A,[`${p}-status-${l}`]:!!l,[`${p}-color-${m}`]:W}),X={};m&&!W&&(X.color=m,X.background=m);const ct=w()(p,{[`${p}-status`]:A,[`${p}-not-a-wrapper`]:!C,[`${p}-rtl`]:M==="rtl"},Z,Tt,s==null?void 0:s.className,(n=s==null?void 0:s.classNames)===null||n===void 0?void 0:n.root,j==null?void 0:j.root,zt,Rt);if(!C&&A){const E=B.color;return rt(r.createElement("span",Object.assign({},nt,{className:ct,style:Object.assign(Object.assign(Object.assign({},O==null?void 0:O.root),(o=s==null?void 0:s.styles)===null||o===void 0?void 0:o.root),B)}),r.createElement("span",{className:Zt,style:Object.assign(Object.assign(Object.assign({},O==null?void 0:O.indicator),(d=s==null?void 0:s.styles)===null||d===void 0?void 0:d.indicator),X)}),c&&r.createElement("span",{style:{color:E},className:`${p}-status-text`},c)))}return rt(r.createElement("span",Object.assign({ref:e},nt,{className:ct,style:Object.assign(Object.assign({},(a=s==null?void 0:s.styles)===null||a===void 0?void 0:a.root),O==null?void 0:O.root)}),C,r.createElement(mt.ZP,{visible:!P,motionName:`${p}-zoom`,motionAppear:!1,motionDeadline:1e3},({className:E})=>{var U,Y;const Ht=st("scroll-number",v),G=lt.current,Mt=w()(j==null?void 0:j.indicator,(U=s==null?void 0:s.classNames)===null||U===void 0?void 0:U.indicator,{[`${p}-dot`]:G,[`${p}-count`]:!G,[`${p}-count-sm`]:f==="small",[`${p}-multiple-words`]:!G&&L&&L.toString().length>1,[`${p}-status-${l}`]:!!l,[`${p}-color-${m}`]:W});let D=Object.assign(Object.assign(Object.assign({},O==null?void 0:O.indicator),(Y=s==null?void 0:s.styles)===null||Y===void 0?void 0:Y.indicator),B);return m&&!W&&(D=D||{},D.background=m),r.createElement(jt,{prefixCls:Ht,show:!P,motionClassName:E,className:Mt,count:L,title:Wt,style:D,key:"scrollNumber"},Ft)}),Dt))});ot.Ribbon=Ot;var Pt=ot}}]);
