# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段 - Nginx
FROM nginx:alpine

# 安装openssl用于SSL证书
RUN apk add --no-cache openssl

# 删除默认nginx配置
RUN rm /etc/nginx/conf.d/default.conf

# 复制构建的应用
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置将在docker-compose中挂载

# 创建SSL目录
RUN mkdir -p /etc/nginx/ssl

# 暴露端口
EXPOSE 8088 8089

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
